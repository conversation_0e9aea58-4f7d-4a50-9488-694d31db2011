const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const Project = require('../models/Project');
const User = require('../models/User');
const Task = require('../models/Task');
const { requirePermission, canAccessProject } = require('../middleware/auth');

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/projects/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|xls|xlsx/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Chỉ cho phép upload file ảnh và tài liệu'));
        }
    }
});

// List all projects
router.get('/', async (req, res) => {
    try {
        const user = req.session.user;
        let query = {};
        
        // Filter projects based on user role
        if (user.role === 'project_manager') {
            query.projectManager = user._id;
        } else if (user.role === 'worker') {
            query['teamMembers.user'] = user._id;
        }
        // Admin can see all projects (no filter)
        
        const projects = await Project.find(query)
            .populate('projectManager', 'fullName email')
            .populate('teamMembers.user', 'fullName email')
            .sort({ createdAt: -1 });
        
        res.render('projects/index', {
            title: 'Danh sách dự án',
            projects: projects,
            user: user
        });
    } catch (error) {
        console.error('Projects list error:', error);
        res.status(500).render('error', {
            title: 'Lỗi',
            message: 'Không thể tải danh sách dự án',
            error: error
        });
    }
});

// Show create project form
router.get('/create', requirePermission('create_project'), async (req, res) => {
    try {
        const projectManagers = await User.find({ 
            role: { $in: ['admin', 'project_manager'] },
            isActive: true 
        }).select('fullName email');
        
        const workers = await User.find({ 
            role: 'worker',
            isActive: true 
        }).select('fullName email');
        
        res.render('projects/create', {
            title: 'Tạo dự án mới',
            projectManagers: projectManagers,
            workers: workers,
            error: null,
            formData: {}
        });
    } catch (error) {
        console.error('Create project form error:', error);
        res.redirect('/projects');
    }
});

// Create new project
router.post('/create', requirePermission('create_project'), async (req, res) => {
    try {
        const {
            name, description, address, clientName, clientPhone, clientEmail,
            totalBudget, startDate, endDate, priority, projectManager, teamMembers
        } = req.body;
        
        // Validation
        if (!name || !description || !address || !clientName || !totalBudget || !startDate || !endDate || !projectManager) {
            const projectManagers = await User.find({ 
                role: { $in: ['admin', 'project_manager'] },
                isActive: true 
            }).select('fullName email');
            
            const workers = await User.find({ 
                role: 'worker',
                isActive: true 
            }).select('fullName email');
            
            return res.render('projects/create', {
                title: 'Tạo dự án mới',
                projectManagers: projectManagers,
                workers: workers,
                error: 'Vui lòng nhập đầy đủ thông tin bắt buộc',
                formData: req.body
            });
        }
        
        // Create project
        const project = new Project({
            name,
            description,
            address,
            client: {
                name: clientName,
                phone: clientPhone || null,
                email: clientEmail || null
            },
            budget: {
                total: parseFloat(totalBudget),
                spent: 0
            },
            timeline: {
                startDate: new Date(startDate),
                endDate: new Date(endDate)
            },
            priority: priority || 'medium',
            projectManager: projectManager,
            teamMembers: teamMembers ? teamMembers.map(userId => ({
                user: userId,
                role: 'worker',
                assignedDate: new Date()
            })) : [],
            createdBy: req.session.user._id
        });
        
        await project.save();
        res.redirect('/projects');
        
    } catch (error) {
        console.error('Create project error:', error);
        const projectManagers = await User.find({ 
            role: { $in: ['admin', 'project_manager'] },
            isActive: true 
        }).select('fullName email');
        
        const workers = await User.find({ 
            role: 'worker',
            isActive: true 
        }).select('fullName email');
        
        res.render('projects/create', {
            title: 'Tạo dự án mới',
            projectManagers: projectManagers,
            workers: workers,
            error: 'Đã xảy ra lỗi, vui lòng thử lại',
            formData: req.body
        });
    }
});

// View project details
router.get('/:id', canAccessProject, async (req, res) => {
    try {
        const project = await Project.findById(req.params.id)
            .populate('projectManager', 'fullName email phone')
            .populate('teamMembers.user', 'fullName email phone role')
            .populate('createdBy', 'fullName email');
        
        const tasks = await Task.find({ project: project._id })
            .populate('assignedTo', 'fullName')
            .sort({ createdAt: -1 });
        
        // Calculate project statistics
        const totalTasks = tasks.length;
        const completedTasks = tasks.filter(task => task.status === 'completed').length;
        const inProgressTasks = tasks.filter(task => task.status === 'in_progress').length;
        const overdueTasks = tasks.filter(task => task.isOverdue()).length;
        
        res.render('projects/detail', {
            title: project.name,
            project: project,
            tasks: tasks,
            stats: {
                totalTasks,
                completedTasks,
                inProgressTasks,
                overdueTasks
            }
        });
    } catch (error) {
        console.error('Project detail error:', error);
        res.redirect('/projects');
    }
});

module.exports = router;
