<%- contentFor('header') %>
<h1 class="page-title"><%= _locals.title %></h1>

<%- contentFor('footer') %>
<h1>This is the footer</h1>

<% /*
Content for the `body` section should either be the first thing defined
in the view, or it has to be declared just like any other section.
*/ %>
<%- contentFor('body') %>

This is part of the body.

<% /*
When style extraction is enabled, any custom styles that a page defines, will
be extracted out of the content and made available in the specific placeholder.
In our example, even though we're defining a <style> within the body, this will
be placed, according to our layout, inside of the <head> element.
<link> blocks to load external stylesheets are also extracted when the option
is enabled.
*/ %>

<style>
  .page-message { color: blue }
</style>

<% /*
Like stylesheets, scripts can also be extracted.
This script block will end up at the end of the HTML document.
*/ %>
<script>
  // Script content!
</script>

<h1 class="page-message"><%= message %></h1>

