// Main JavaScript file for Construction Project Management

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);

    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('[data-confirm-delete]');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm-delete') || 'Bạn có chắc chắn muốn xóa?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Progress bar animations
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(function(bar) {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(function() {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, 100);
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // File upload preview
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        input.addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.getElementById(input.id + '-preview');
            
            if (preview && files.length > 0) {
                preview.innerHTML = '';
                
                Array.from(files).forEach(function(file) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-preview-item';
                    fileItem.innerHTML = `
                        <i class="fas fa-file me-2"></i>
                        <span>${file.name}</span>
                        <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                    `;
                    preview.appendChild(fileItem);
                });
            }
        });
    });

    // Dynamic form fields
    const addFieldButtons = document.querySelectorAll('[data-add-field]');
    addFieldButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-add-field');
            const container = document.getElementById(targetId);
            const template = container.querySelector('.field-template');
            
            if (template) {
                const newField = template.cloneNode(true);
                newField.classList.remove('field-template', 'd-none');
                
                // Update field names and IDs
                const inputs = newField.querySelectorAll('input, select, textarea');
                inputs.forEach(function(input) {
                    const name = input.getAttribute('name');
                    if (name) {
                        const index = container.children.length - 1;
                        input.setAttribute('name', name.replace('[]', `[${index}]`));
                    }
                });
                
                container.appendChild(newField);
            }
        });
    });

    // Remove field buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-field') || e.target.closest('.remove-field')) {
            const fieldGroup = e.target.closest('.field-group');
            if (fieldGroup) {
                fieldGroup.remove();
            }
        }
    });

    // Search functionality
    const searchInputs = document.querySelectorAll('[data-search]');
    searchInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const targetSelector = this.getAttribute('data-search');
            const items = document.querySelectorAll(targetSelector);
            
            items.forEach(function(item) {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Status update functionality
    const statusSelects = document.querySelectorAll('.status-update');
    statusSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            const taskId = this.getAttribute('data-task-id');
            const status = this.value;
            
            if (taskId && status) {
                updateTaskStatus(taskId, status);
            }
        });
    });

    // Progress update functionality
    const progressInputs = document.querySelectorAll('.progress-update');
    progressInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            const taskId = this.getAttribute('data-task-id');
            const progress = this.value;
            
            if (taskId && progress !== '') {
                updateTaskProgress(taskId, progress);
            }
        });
    });
});

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || document.body;
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

function updateTaskStatus(taskId, status) {
    fetch(`/tasks/${taskId}/progress`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Cập nhật trạng thái thành công', 'success');
            // Reload page to reflect changes
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Có lỗi xảy ra khi cập nhật trạng thái', 'danger');
    });
}

function updateTaskProgress(taskId, progress) {
    fetch(`/tasks/${taskId}/progress`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ progress: progress })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Cập nhật tiến độ thành công', 'success');
            // Update progress bar
            const progressBar = document.querySelector(`[data-task-id="${taskId}"] .progress-bar`);
            if (progressBar) {
                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);
            }
        } else {
            showAlert('Có lỗi xảy ra khi cập nhật tiến độ', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Có lỗi xảy ra khi cập nhật tiến độ', 'danger');
    });
}

function addNote(taskId, content) {
    fetch(`/tasks/${taskId}/notes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: content })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Thêm ghi chú thành công', 'success');
            // Add note to the list
            const notesList = document.getElementById('notes-list');
            if (notesList) {
                const noteElement = createNoteElement(data.note);
                notesList.insertBefore(noteElement, notesList.firstChild);
            }
            // Clear the form
            document.getElementById('note-content').value = '';
        } else {
            showAlert('Có lỗi xảy ra khi thêm ghi chú', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Có lỗi xảy ra khi thêm ghi chú', 'danger');
    });
}

function createNoteElement(note) {
    const noteDiv = document.createElement('div');
    noteDiv.className = 'note-item mb-3 p-3 border rounded';
    noteDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <p class="mb-1">${note.content}</p>
                <small class="text-muted">
                    <i class="fas fa-user me-1"></i>
                    ${note.createdBy.fullName} - 
                    ${new Date(note.createdAt).toLocaleString('vi-VN')}
                </small>
            </div>
        </div>
    `;
    return noteDiv;
}

// Export functions for global use
window.showAlert = showAlert;
window.updateTaskStatus = updateTaskStatus;
window.updateTaskProgress = updateTaskProgress;
window.addNote = addNote;
