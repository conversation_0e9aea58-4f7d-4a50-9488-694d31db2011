/* Custom styles for Construction Project Management */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

/* Card Styles */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Status Badges */
.status-pending {
    background-color: var(--warning-color) !important;
    color: #212529 !important;
}

.status-in_progress {
    background-color: var(--info-color) !important;
}

.status-completed {
    background-color: var(--success-color) !important;
}

.status-cancelled {
    background-color: var(--dark-color) !important;
}

.status-on_hold {
    background-color: var(--warning-color) !important;
    color: #212529 !important;
}

.status-planning {
    background-color: #6c757d !important;
}

/* Priority Borders */
.priority-low {
    border-left: 4px solid var(--info-color) !important;
}

.priority-medium {
    border-left: 4px solid var(--warning-color) !important;
}

.priority-high {
    border-left: 4px solid #fd7e14 !important;
}

.priority-urgent {
    border-left: 4px solid var(--danger-color) !important;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.3);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

/* Navigation */
.nav-link {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: white;
    transform: translateX(5px);
}

.nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Dropdown */
.dropdown-menu {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* File Upload */
.file-upload-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.file-upload-wrapper input[type=file] {
    position: absolute;
    left: -9999px;
}

.file-upload-label {
    cursor: pointer;
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--primary-color);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(102, 126, 234, 0.3);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Weather Icons */
.weather-sunny { color: #ffc107; }
.weather-cloudy { color: #6c757d; }
.weather-rainy { color: #007bff; }
.weather-stormy { color: #dc3545; }
.weather-foggy { color: #6f42c1; }

/* Material Icons */
.material-icon {
    font-size: 1.2rem;
    vertical-align: middle;
    margin-right: 0.5rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
