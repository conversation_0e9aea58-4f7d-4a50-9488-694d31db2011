<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - <PERSON><PERSON><PERSON><PERSON> lý Dự án <PERSON> dựng</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
            color: #495057 !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn {
            border-radius: 10px;
            font-weight: 500;
        }
        .progress {
            height: 8px;
            border-radius: 10px;
        }
        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
        }
        .status-pending { background-color: #ffc107; }
        .status-in-progress { background-color: #007bff; }
        .status-completed { background-color: #28a745; }
        .status-cancelled { background-color: #6c757d; }
        .priority-low { border-left: 4px solid #17a2b8; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-high { border-left: 4px solid #fd7e14; }
        .priority-urgent { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <% if (user) { %>
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-hard-hat me-2"></i>
                            Quản lý Dự án
                        </h5>
                        <small class="text-white-50">Xây dựng Nhà cửa</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/projects">
                                <i class="fas fa-building me-2"></i>
                                Dự án
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/tasks">
                                <i class="fas fa-tasks me-2"></i>
                                Nhiệm vụ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/reports">
                                <i class="fas fa-chart-line me-2"></i>
                                Báo cáo
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard/calendar">
                                <i class="fas fa-calendar me-2"></i>
                                Lịch công việc
                            </a>
                        </li>
                        
                        <% if (user.role === 'admin' || user.role === 'project_manager') { %>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                        <li class="nav-item">
                            <a class="nav-link" href="/projects/create">
                                <i class="fas fa-plus me-2"></i>
                                Tạo dự án
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/tasks/create">
                                <i class="fas fa-plus me-2"></i>
                                Tạo nhiệm vụ
                            </a>
                        </li>
                        <% } %>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="/reports/create">
                                <i class="fas fa-plus me-2"></i>
                                Tạo báo cáo
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/auth/profile">
                                <i class="fas fa-user me-2"></i>
                                Thông tin cá nhân
                            </a>
                        </li>
                        <li class="nav-item">
                            <form action="/auth/logout" method="POST" class="d-inline">
                                <button type="submit" class="nav-link btn btn-link text-start w-100 border-0">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Đăng xuất
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>
            <% } %>
            
            <!-- Main content -->
            <main class="<%= user ? 'col-md-9 ms-sm-auto col-lg-10 px-md-4' : 'col-12' %> main-content">
                <% if (user) { %>
                <!-- Top navbar -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><%= title %></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i>
                                <%= user.fullName %>
                                <span class="badge bg-primary ms-2"><%= user.role %></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/auth/profile">
                                    <i class="fas fa-user me-2"></i>Thông tin cá nhân
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form action="/auth/logout" method="POST" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <% } %>
                
                <!-- Page content -->
                <%- body %>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/app.js"></script>
    
    <script>
        // Set active nav link
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 5000);
    </script>
</body>
</html>
