const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const Project = require('../models/Project');
const User = require('../models/User');
const { requirePermission, canAccessTask, canAccessProject } = require('../middleware/auth');

// List tasks (filtered by user role)
router.get('/', async (req, res) => {
    try {
        const user = req.session.user;
        let query = {};
        
        // Filter tasks based on user role
        if (user.role === 'worker') {
            query.assignedTo = user._id;
        } else if (user.role === 'project_manager') {
            // Get projects managed by this user
            const projects = await Project.find({ projectManager: user._id }).select('_id');
            query.project = { $in: projects.map(p => p._id) };
        }
        // Admin can see all tasks (no filter)
        
        const tasks = await Task.find(query)
            .populate('project', 'name')
            .populate('assignedTo', 'fullName')
            .populate('assignedBy', 'fullName')
            .sort({ createdAt: -1 });
        
        res.render('tasks/index', {
            title: '<PERSON>h sách nhiệm vụ',
            tasks: tasks,
            user: user
        });
    } catch (error) {
        console.error('Tasks list error:', error);
        res.status(500).render('error', {
            title: 'Lỗi',
            message: 'Không thể tải danh sách nhiệm vụ',
            error: error
        });
    }
});

// Show create task form
router.get('/create', requirePermission('assign_tasks'), async (req, res) => {
    try {
        const user = req.session.user;
        let projectQuery = {};
        
        // Project managers can only create tasks for their projects
        if (user.role === 'project_manager') {
            projectQuery.projectManager = user._id;
        }
        
        const projects = await Project.find(projectQuery)
            .populate('teamMembers.user', 'fullName email')
            .select('name teamMembers');
        
        const workers = await User.find({ 
            role: 'worker',
            isActive: true 
        }).select('fullName email');
        
        res.render('tasks/create', {
            title: 'Tạo nhiệm vụ mới',
            projects: projects,
            workers: workers,
            error: null,
            formData: {}
        });
    } catch (error) {
        console.error('Create task form error:', error);
        res.redirect('/tasks');
    }
});

// Create new task
router.post('/create', requirePermission('assign_tasks'), async (req, res) => {
    try {
        const {
            title, description, project, assignedTo, category, priority,
            startDate, endDate, estimatedHours, materials
        } = req.body;
        
        // Validation
        if (!title || !description || !project || !assignedTo || !category || !startDate || !endDate || !estimatedHours) {
            const user = req.session.user;
            let projectQuery = {};
            
            if (user.role === 'project_manager') {
                projectQuery.projectManager = user._id;
            }
            
            const projects = await Project.find(projectQuery)
                .populate('teamMembers.user', 'fullName email')
                .select('name teamMembers');
            
            const workers = await User.find({ 
                role: 'worker',
                isActive: true 
            }).select('fullName email');
            
            return res.render('tasks/create', {
                title: 'Tạo nhiệm vụ mới',
                projects: projects,
                workers: workers,
                error: 'Vui lòng nhập đầy đủ thông tin bắt buộc',
                formData: req.body
            });
        }
        
        // Parse materials if provided
        let parsedMaterials = [];
        if (materials && Array.isArray(materials)) {
            parsedMaterials = materials.filter(m => m.name && m.quantity && m.unit).map(m => ({
                name: m.name,
                quantity: parseFloat(m.quantity),
                unit: m.unit,
                cost: parseFloat(m.cost) || 0
            }));
        }
        
        // Create task
        const task = new Task({
            title,
            description,
            project,
            assignedTo,
            assignedBy: req.session.user._id,
            category,
            priority: priority || 'medium',
            timeline: {
                startDate: new Date(startDate),
                endDate: new Date(endDate)
            },
            estimatedHours: parseFloat(estimatedHours),
            materials: parsedMaterials
        });
        
        await task.save();
        res.redirect('/tasks');
        
    } catch (error) {
        console.error('Create task error:', error);
        const user = req.session.user;
        let projectQuery = {};
        
        if (user.role === 'project_manager') {
            projectQuery.projectManager = user._id;
        }
        
        const projects = await Project.find(projectQuery)
            .populate('teamMembers.user', 'fullName email')
            .select('name teamMembers');
        
        const workers = await User.find({ 
            role: 'worker',
            isActive: true 
        }).select('fullName email');
        
        res.render('tasks/create', {
            title: 'Tạo nhiệm vụ mới',
            projects: projects,
            workers: workers,
            error: 'Đã xảy ra lỗi, vui lòng thử lại',
            formData: req.body
        });
    }
});

// View task details
router.get('/:id', canAccessTask, async (req, res) => {
    try {
        const task = await Task.findById(req.params.id)
            .populate('project', 'name')
            .populate('assignedTo', 'fullName email phone')
            .populate('assignedBy', 'fullName email')
            .populate('notes.createdBy', 'fullName');
        
        res.render('tasks/detail', {
            title: task.title,
            task: task
        });
    } catch (error) {
        console.error('Task detail error:', error);
        res.redirect('/tasks');
    }
});

// Update task progress
router.post('/:id/progress', canAccessTask, async (req, res) => {
    try {
        const { progress, status, notes } = req.body;
        const task = await Task.findById(req.params.id);
        
        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }
        
        // Update progress and status
        if (progress !== undefined) {
            task.progress = Math.max(0, Math.min(100, parseInt(progress)));
        }
        
        if (status) {
            task.status = status;
            
            // Set actual dates based on status
            if (status === 'in_progress' && !task.timeline.actualStartDate) {
                task.timeline.actualStartDate = new Date();
            } else if (status === 'completed') {
                task.timeline.actualEndDate = new Date();
                task.progress = 100;
            }
        }
        
        // Add notes if provided
        if (notes && notes.trim()) {
            task.notes.push({
                content: notes.trim(),
                createdBy: req.session.user._id
            });
        }
        
        await task.save();
        
        // Update project progress
        const project = await Project.findById(task.project);
        if (project) {
            await project.calculateProgress();
            await project.save();
        }
        
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            res.json({ success: true, task: task });
        } else {
            res.redirect(`/tasks/${task._id}`);
        }
        
    } catch (error) {
        console.error('Update task progress error:', error);
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            res.status(500).json({ error: 'Server error' });
        } else {
            res.redirect('/tasks');
        }
    }
});

// Add note to task
router.post('/:id/notes', canAccessTask, async (req, res) => {
    try {
        const { content } = req.body;
        
        if (!content || !content.trim()) {
            return res.status(400).json({ error: 'Note content is required' });
        }
        
        const task = await Task.findById(req.params.id);
        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }
        
        task.notes.push({
            content: content.trim(),
            createdBy: req.session.user._id
        });
        
        await task.save();
        
        const populatedTask = await Task.findById(task._id)
            .populate('notes.createdBy', 'fullName');
        
        res.json({ 
            success: true, 
            note: populatedTask.notes[populatedTask.notes.length - 1]
        });
        
    } catch (error) {
        console.error('Add note error:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

module.exports = router;
