const User = require('../models/User');

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
    if (!req.session.user) {
        if (req.xhr || req.headers.accept.indexOf('json') > -1) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        return res.redirect('/auth/login');
    }
    next();
};

// Middleware to check if user is not authenticated (for login/register pages)
const requireGuest = (req, res, next) => {
    if (req.session.user) {
        return res.redirect('/dashboard');
    }
    next();
};

// Middleware to check user role
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.session.user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }
        
        if (!roles.includes(req.session.user.role)) {
            return res.status(403).json({ error: 'Forbidden' });
        }
        
        next();
    };
};

// Middleware to check specific permission
const requirePermission = (permission) => {
    return async (req, res, next) => {
        try {
            if (!req.session.user) {
                return res.status(401).json({ error: 'Unauthorized' });
            }
            
            const user = await User.findById(req.session.user._id);
            if (!user || !user.hasPermission(permission)) {
                return res.status(403).json({ error: 'Insufficient permissions' });
            }
            
            next();
        } catch (error) {
            console.error('Permission check error:', error);
            res.status(500).json({ error: 'Server error' });
        }
    };
};

// Middleware to load current user data
const loadUser = async (req, res, next) => {
    try {
        if (req.session.user) {
            const user = await User.findById(req.session.user._id).select('-password');
            if (user) {
                req.user = user;
                res.locals.currentUser = user;
            } else {
                // User not found, clear session
                req.session.destroy();
            }
        }
        next();
    } catch (error) {
        console.error('Load user error:', error);
        next();
    }
};

// Middleware to check if user can access project
const canAccessProject = async (req, res, next) => {
    try {
        const Project = require('../models/Project');
        const projectId = req.params.id || req.params.projectId;
        
        if (!projectId) {
            return res.status(400).json({ error: 'Project ID required' });
        }
        
        const project = await Project.findById(projectId);
        if (!project) {
            return res.status(404).json({ error: 'Project not found' });
        }
        
        const user = req.session.user;
        
        // Admin can access all projects
        if (user.role === 'admin') {
            req.project = project;
            return next();
        }
        
        // Project manager can access their projects
        if (user.role === 'project_manager' && project.projectManager.toString() === user._id) {
            req.project = project;
            return next();
        }
        
        // Workers can access projects they're assigned to
        const isTeamMember = project.teamMembers.some(member => 
            member.user.toString() === user._id
        );
        
        if (isTeamMember) {
            req.project = project;
            return next();
        }
        
        return res.status(403).json({ error: 'Access denied to this project' });
        
    } catch (error) {
        console.error('Project access check error:', error);
        res.status(500).json({ error: 'Server error' });
    }
};

// Middleware to check if user can access task
const canAccessTask = async (req, res, next) => {
    try {
        const Task = require('../models/Task');
        const taskId = req.params.id || req.params.taskId;
        
        if (!taskId) {
            return res.status(400).json({ error: 'Task ID required' });
        }
        
        const task = await Task.findById(taskId).populate('project');
        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }
        
        const user = req.session.user;
        
        // Admin can access all tasks
        if (user.role === 'admin') {
            req.task = task;
            return next();
        }
        
        // Project manager can access tasks in their projects
        if (user.role === 'project_manager' && task.project.projectManager.toString() === user._id) {
            req.task = task;
            return next();
        }
        
        // Workers can access tasks assigned to them
        if (task.assignedTo.toString() === user._id) {
            req.task = task;
            return next();
        }
        
        return res.status(403).json({ error: 'Access denied to this task' });
        
    } catch (error) {
        console.error('Task access check error:', error);
        res.status(500).json({ error: 'Server error' });
    }
};

module.exports = {
    requireAuth,
    requireGuest,
    requireRole,
    requirePermission,
    loadUser,
    canAccessProject,
    canAccessTask
};
