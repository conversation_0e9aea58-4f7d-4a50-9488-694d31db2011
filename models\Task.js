const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        maxlength: 1000
    },
    project: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Project',
        required: true
    },
    assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    assignedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    category: {
        type: String,
        enum: ['foundation', 'structure', 'roofing', 'electrical', 'plumbing', 'finishing', 'other'],
        required: true
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    status: {
        type: String,
        enum: ['pending', 'in_progress', 'review', 'completed', 'cancelled'],
        default: 'pending'
    },
    progress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    timeline: {
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date,
            required: true
        },
        actualStartDate: {
            type: Date,
            default: null
        },
        actualEndDate: {
            type: Date,
            default: null
        }
    },
    estimatedHours: {
        type: Number,
        required: true,
        min: 0
    },
    actualHours: {
        type: Number,
        default: 0,
        min: 0
    },
    materials: [{
        name: {
            type: String,
            required: true
        },
        quantity: {
            type: Number,
            required: true,
            min: 0
        },
        unit: {
            type: String,
            required: true
        },
        cost: {
            type: Number,
            default: 0,
            min: 0
        }
    }],
    dependencies: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Task'
    }],
    notes: [{
        content: {
            type: String,
            required: true,
            maxlength: 500
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    attachments: [{
        name: {
            type: String,
            required: true
        },
        path: {
            type: String,
            required: true
        },
        uploadedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update updatedAt before saving
taskSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Check if task is overdue
taskSchema.methods.isOverdue = function() {
    return new Date() > this.timeline.endDate && this.status !== 'completed';
};

// Calculate task duration
taskSchema.methods.getDuration = function() {
    const start = this.timeline.actualStartDate || this.timeline.startDate;
    const end = this.timeline.actualEndDate || this.timeline.endDate;
    return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
};

// Get total material cost
taskSchema.methods.getTotalMaterialCost = function() {
    return this.materials.reduce((total, material) => {
        return total + (material.quantity * material.cost);
    }, 0);
};

// Check if task can start (dependencies completed)
taskSchema.methods.canStart = async function() {
    if (this.dependencies.length === 0) return true;
    
    const Task = mongoose.model('Task');
    const dependencies = await Task.find({
        _id: { $in: this.dependencies }
    });
    
    return dependencies.every(dep => dep.status === 'completed');
};

module.exports = mongoose.model('Task', taskSchema);
