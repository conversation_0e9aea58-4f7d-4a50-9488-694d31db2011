<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Thông tin cá nhân
                </h5>
            </div>
            
            <div class="card-body">
                <% if (success) { %>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <%= success %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <% } %>
                
                <% if (error) { %>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <%= error %>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <% } %>
                
                <form action="/auth/profile" method="POST" class="needs-validation" novalidate>
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">Tên đăng nhập</label>
                            <input type="text" class="form-control" id="username" 
                                   value="<%= user.username %>" readonly>
                            <div class="form-text">Không thể thay đổi tên đăng nhập</div>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" 
                                   value="<%= user.email %>" readonly>
                            <div class="form-text">Không thể thay đổi email</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="fullName" class="form-label">Họ và tên *</label>
                            <input type="text" class="form-control" id="fullName" name="fullName" 
                                   value="<%= user.fullName %>" required>
                            <div class="invalid-feedback">
                                Vui lòng nhập họ và tên
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label">Số điện thoại</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<%= user.phone || '' %>">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="role" class="form-label">Vai trò</label>
                            <input type="text" class="form-control" id="role" 
                                   value="<%= user.role %>" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="lastLogin" class="form-label">Lần đăng nhập cuối</label>
                            <input type="text" class="form-control" id="lastLogin" 
                                   value="<%= user.lastLogin ? moment(user.lastLogin).format('DD/MM/YYYY HH:mm') : 'Chưa có' %>" readonly>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- Change Password Section -->
                    <h6 class="mb-3">
                        <i class="fas fa-lock me-2"></i>
                        Đổi mật khẩu
                    </h6>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="currentPassword" class="form-label">Mật khẩu hiện tại</label>
                            <input type="password" class="form-control" id="currentPassword" name="currentPassword">
                            <div class="form-text">Chỉ nhập nếu bạn muốn đổi mật khẩu</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="newPassword" class="form-label">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="newPassword" name="newPassword">
                            <div class="form-text">Tối thiểu 6 ký tự</div>
                        </div>
                        <div class="col-md-6">
                            <label for="confirmPassword" class="form-label">Xác nhận mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword">
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/dashboard" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>
                            Hủy
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Cập nhật thông tin
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Account Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Thống kê tài khoản
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-primary mb-1">
                                <%= moment(user.createdAt).fromNow() %>
                            </h4>
                            <small class="text-muted">Tham gia</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-success mb-1">
                                <span id="userProjects">-</span>
                            </h4>
                            <small class="text-muted">Dự án</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-info mb-1">
                                <span id="userTasks">-</span>
                            </h4>
                            <small class="text-muted">Nhiệm vụ</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-warning mb-1">
                                <span id="userReports">-</span>
                            </h4>
                            <small class="text-muted">Báo cáo</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('newPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const currentPassword = document.getElementById('currentPassword');
    
    function validatePasswords() {
        if (newPassword.value && newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
        } else {
            confirmPassword.setCustomValidity('');
        }
        
        if (newPassword.value && !currentPassword.value) {
            currentPassword.setCustomValidity('Vui lòng nhập mật khẩu hiện tại');
        } else {
            currentPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    currentPassword.addEventListener('input', validatePasswords);
    
    // Load user statistics
    loadUserStats();
});

function loadUserStats() {
    // This would typically be loaded via AJAX
    // For now, we'll just show placeholders
    document.getElementById('userProjects').textContent = '0';
    document.getElementById('userTasks').textContent = '0';
    document.getElementById('userReports').textContent = '0';
}
</script>

<style>
.stat-item {
    padding: 1rem;
    border-radius: 10px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

.form-control[readonly] {
    background-color: #f8f9fa;
    opacity: 1;
}
</style>
