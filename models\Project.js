const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        maxlength: 1000
    },
    address: {
        type: String,
        required: true,
        maxlength: 500
    },
    client: {
        name: {
            type: String,
            required: true,
            maxlength: 100
        },
        phone: {
            type: String,
            maxlength: 15
        },
        email: {
            type: String,
            maxlength: 100
        }
    },
    budget: {
        total: {
            type: Number,
            required: true,
            min: 0
        },
        spent: {
            type: Number,
            default: 0,
            min: 0
        }
    },
    timeline: {
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date,
            required: true
        },
        actualStartDate: {
            type: Date,
            default: null
        },
        actualEndDate: {
            type: Date,
            default: null
        }
    },
    status: {
        type: String,
        enum: ['planning', 'in_progress', 'on_hold', 'completed', 'cancelled'],
        default: 'planning'
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium'
    },
    progress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    projectManager: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    teamMembers: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        role: {
            type: String,
            enum: ['supervisor', 'worker', 'specialist'],
            default: 'worker'
        },
        assignedDate: {
            type: Date,
            default: Date.now
        }
    }],
    documents: [{
        name: {
            type: String,
            required: true
        },
        path: {
            type: String,
            required: true
        },
        uploadedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    images: [{
        name: {
            type: String,
            required: true
        },
        path: {
            type: String,
            required: true
        },
        description: {
            type: String,
            maxlength: 200
        },
        uploadedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update updatedAt before saving
projectSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Calculate project progress based on tasks
projectSchema.methods.calculateProgress = async function() {
    const Task = mongoose.model('Task');
    const tasks = await Task.find({ project: this._id });
    
    if (tasks.length === 0) {
        this.progress = 0;
        return 0;
    }
    
    const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0);
    this.progress = Math.round(totalProgress / tasks.length);
    
    return this.progress;
};

// Check if project is overdue
projectSchema.methods.isOverdue = function() {
    return new Date() > this.timeline.endDate && this.status !== 'completed';
};

// Get project duration in days
projectSchema.methods.getDuration = function() {
    const start = this.timeline.actualStartDate || this.timeline.startDate;
    const end = this.timeline.actualEndDate || this.timeline.endDate;
    return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
};

module.exports = mongoose.model('Project', projectSchema);
