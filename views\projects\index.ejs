<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><PERSON><PERSON> sách dự án</h2>
    <% if (user.role === 'admin' || user.role === 'project_manager') { %>
    <a href="/projects/create" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Tạo dự án mới
    </a>
    <% } %>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="Tìm kiếm dự án..." 
                   data-search=".project-card">
        </div>
    </div>
    <div class="col-md-6">
        <select class="form-select" id="statusFilter">
            <option value="">T<PERSON><PERSON> cả trạng thái</option>
            <option value="planning"><PERSON><PERSON><PERSON> kế hoạch</option>
            <option value="in_progress"><PERSON><PERSON> thực hiện</option>
            <option value="on_hold">Tạm dừng</option>
            <option value="completed">Hoàn thành</option>
            <option value="cancelled">Đã hủy</option>
        </select>
    </div>
</div>

<!-- Projects Grid -->
<div class="row">
    <% if (projects.length > 0) { %>
    <% projects.forEach(project => { %>
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card project-card priority-<%= project.priority %>" data-status="<%= project.status %>">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <a href="/projects/<%= project._id %>" class="text-decoration-none">
                        <%= project.name %>
                    </a>
                </h6>
                <span class="badge status-<%= project.status %>">
                    <%= project.status %>
                </span>
            </div>
            
            <div class="card-body">
                <p class="text-muted mb-3">
                    <%= project.description.length > 100 ? project.description.substring(0, 100) + '...' : project.description %>
                </p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Tiến độ</small>
                        <small class="text-muted"><%= project.progress %>%</small>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" 
                             style="width: <%= project.progress %>%" 
                             aria-valuenow="<%= project.progress %>" 
                             aria-valuemin="0" aria-valuemax="100">
                        </div>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted d-block">Ngân sách</small>
                        <strong><%= (project.budget.total / 1000000).toFixed(1) %>M VNĐ</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">Hạn chót</small>
                        <strong class="<%= moment(project.timeline.endDate).isBefore(moment()) && project.status !== 'completed' ? 'text-danger' : '' %>">
                            <%= moment(project.timeline.endDate).format('DD/MM/YYYY') %>
                        </strong>
                    </div>
                </div>
            </div>
            
            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            <%= project.projectManager ? project.projectManager.fullName : 'Chưa phân công' %>
                        </small>
                    </div>
                    <div>
                        <span class="badge bg-secondary">
                            <i class="fas fa-users me-1"></i>
                            <%= project.teamMembers.length %>
                        </span>
                        <% if (project.priority === 'urgent') { %>
                        <span class="badge bg-danger ms-1">
                            <i class="fas fa-exclamation-triangle"></i>
                        </span>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <% }); %>
    <% } else { %>
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-building fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">Chưa có dự án nào</h4>
            <p class="text-muted">
                <% if (user.role === 'admin' || user.role === 'project_manager') { %>
                Hãy tạo dự án đầu tiên của bạn.
                <% } else { %>
                Bạn chưa được phân công vào dự án nào.
                <% } %>
            </p>
            <% if (user.role === 'admin' || user.role === 'project_manager') { %>
            <a href="/projects/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Tạo dự án mới
            </a>
            <% } %>
        </div>
    </div>
    <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status filter
    const statusFilter = document.getElementById('statusFilter');
    statusFilter.addEventListener('change', function() {
        const selectedStatus = this.value;
        const projectCards = document.querySelectorAll('.project-card');
        
        projectCards.forEach(card => {
            const cardStatus = card.getAttribute('data-status');
            if (selectedStatus === '' || cardStatus === selectedStatus) {
                card.closest('.col-lg-6').style.display = '';
            } else {
                card.closest('.col-lg-6').style.display = 'none';
            }
        });
    });
    
    // Search functionality
    const searchInput = document.querySelector('[data-search]');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const projectCards = document.querySelectorAll('.project-card');
        
        projectCards.forEach(card => {
            const projectName = card.querySelector('h6 a').textContent.toLowerCase();
            const projectDesc = card.querySelector('.text-muted').textContent.toLowerCase();
            
            if (projectName.includes(searchTerm) || projectDesc.includes(searchTerm)) {
                card.closest('.col-lg-6').style.display = '';
            } else {
                card.closest('.col-lg-6').style.display = 'none';
            }
        });
    });
});
</script>
