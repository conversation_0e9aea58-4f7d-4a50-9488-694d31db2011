const express = require('express');
const path = require('path');
const expressLayouts = require('express-ejs-layouts');

const app = express();

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// EJS layout support
app.use(expressLayouts);
app.set('layout', 'layouts/main');

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Body parsing middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Mock user for testing
app.use((req, res, next) => {
    res.locals.user = {
        _id: '123',
        username: 'testuser',
        fullName: 'Test User',
        role: 'admin',
        email: '<EMAIL>'
    };
    res.locals.moment = require('moment');
    next();
});

// Home route
app.get('/', (req, res) => {
    res.render('dashboard/index', {
        title: 'Dashboard',
        user: res.locals.user,
        stats: {
            totalProjects: 5,
            activeProjects: 3,
            totalTasks: 15,
            completedTasks: 8,
            totalUsers: 10,
            totalReports: 20
        },
        projects: [],
        tasks: [],
        recentReports: [],
        progressStats: {
            averageProjectProgress: 65,
            averageTaskProgress: 70
        }
    });
});

// Login route
app.get('/auth/login', (req, res) => {
    res.render('auth/login', {
        title: 'Đăng nhập',
        error: null,
        layout: false
    });
});

// Register route
app.get('/auth/register', (req, res) => {
    res.render('auth/register', {
        title: 'Đăng ký',
        error: null,
        formData: {},
        layout: false
    });
});

// Projects route
app.get('/projects', (req, res) => {
    res.render('projects/index', {
        title: 'Danh sách dự án',
        projects: [],
        user: res.locals.user
    });
});

// Profile route
app.get('/auth/profile', (req, res) => {
    res.render('auth/profile', {
        title: 'Thông tin cá nhân',
        user: res.locals.user,
        success: null,
        error: null
    });
});

// Error handling
app.use((req, res) => {
    res.status(404).render('error', {
        title: 'Không tìm thấy trang',
        message: 'Trang bạn tìm kiếm không tồn tại',
        error: {}
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server đang chạy trên port ${PORT}`);
    console.log(`Truy cập: http://localhost:${PORT}`);
});

module.exports = app;
