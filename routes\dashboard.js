const express = require('express');
const router = express.Router();
const Project = require('../models/Project');
const Task = require('../models/Task');
const Report = require('../models/Report');
const User = require('../models/User');

// Dashboard home
router.get('/', async (req, res) => {
    try {
        const user = req.session.user;
        let stats = {};
        let recentActivities = [];
        let projects = [];
        let tasks = [];
        
        if (user.role === 'admin') {
            // Admin dashboard - overview of everything
            stats = {
                totalProjects: await Project.countDocuments(),
                activeProjects: await Project.countDocuments({ status: 'in_progress' }),
                totalTasks: await Task.countDocuments(),
                completedTasks: await Task.countDocuments({ status: 'completed' }),
                totalUsers: await User.countDocuments({ isActive: true }),
                totalReports: await Report.countDocuments()
            };
            
            projects = await Project.find()
                .populate('projectManager', 'fullName')
                .sort({ updatedAt: -1 })
                .limit(5);
                
            tasks = await Task.find()
                .populate('project', 'name')
                .populate('assignedTo', 'fullName')
                .sort({ updatedAt: -1 })
                .limit(10);
                
        } else if (user.role === 'project_manager') {
            // Project manager dashboard
            const managedProjects = await Project.find({ projectManager: user._id });
            const projectIds = managedProjects.map(p => p._id);
            
            stats = {
                totalProjects: managedProjects.length,
                activeProjects: managedProjects.filter(p => p.status === 'in_progress').length,
                totalTasks: await Task.countDocuments({ project: { $in: projectIds } }),
                completedTasks: await Task.countDocuments({ 
                    project: { $in: projectIds }, 
                    status: 'completed' 
                }),
                overdueProjects: managedProjects.filter(p => p.isOverdue()).length,
                totalReports: await Report.countDocuments({ project: { $in: projectIds } })
            };
            
            projects = managedProjects.sort((a, b) => b.updatedAt - a.updatedAt).slice(0, 5);
            
            tasks = await Task.find({ project: { $in: projectIds } })
                .populate('project', 'name')
                .populate('assignedTo', 'fullName')
                .sort({ updatedAt: -1 })
                .limit(10);
                
        } else if (user.role === 'worker') {
            // Worker dashboard
            const assignedTasks = await Task.find({ assignedTo: user._id });
            const projectIds = [...new Set(assignedTasks.map(t => t.project))];
            
            stats = {
                totalTasks: assignedTasks.length,
                completedTasks: assignedTasks.filter(t => t.status === 'completed').length,
                inProgressTasks: assignedTasks.filter(t => t.status === 'in_progress').length,
                overdueTasks: assignedTasks.filter(t => t.isOverdue()).length,
                totalProjects: projectIds.length,
                totalReports: await Report.countDocuments({ reportedBy: user._id })
            };
            
            projects = await Project.find({ _id: { $in: projectIds } })
                .populate('projectManager', 'fullName')
                .sort({ updatedAt: -1 })
                .limit(5);
                
            tasks = assignedTasks.sort((a, b) => b.updatedAt - a.updatedAt).slice(0, 10);
            await Task.populate(tasks, [
                { path: 'project', select: 'name' },
                { path: 'assignedBy', select: 'fullName' }
            ]);
        }
        
        // Get recent reports
        let reportQuery = {};
        if (user.role === 'worker') {
            reportQuery.reportedBy = user._id;
        } else if (user.role === 'project_manager') {
            const managedProjects = await Project.find({ projectManager: user._id }).select('_id');
            reportQuery.project = { $in: managedProjects.map(p => p._id) };
        }
        
        const recentReports = await Report.find(reportQuery)
            .populate('project', 'name')
            .populate('reportedBy', 'fullName')
            .sort({ createdAt: -1 })
            .limit(5);
        
        // Calculate progress statistics
        const progressStats = {
            averageProjectProgress: 0,
            averageTaskProgress: 0
        };
        
        if (projects.length > 0) {
            const totalProjectProgress = projects.reduce((sum, project) => sum + project.progress, 0);
            progressStats.averageProjectProgress = Math.round(totalProjectProgress / projects.length);
        }
        
        if (tasks.length > 0) {
            const totalTaskProgress = tasks.reduce((sum, task) => sum + task.progress, 0);
            progressStats.averageTaskProgress = Math.round(totalTaskProgress / tasks.length);
        }
        
        res.render('dashboard/index', {
            title: 'Dashboard',
            user: user,
            stats: stats,
            projects: projects,
            tasks: tasks,
            recentReports: recentReports,
            progressStats: progressStats
        });
        
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', {
            title: 'Lỗi Dashboard',
            message: 'Không thể tải dashboard',
            error: error
        });
    }
});

// API endpoint for dashboard statistics (for AJAX updates)
router.get('/api/stats', async (req, res) => {
    try {
        const user = req.session.user;
        let stats = {};
        
        if (user.role === 'admin') {
            stats = {
                totalProjects: await Project.countDocuments(),
                activeProjects: await Project.countDocuments({ status: 'in_progress' }),
                totalTasks: await Task.countDocuments(),
                completedTasks: await Task.countDocuments({ status: 'completed' }),
                totalUsers: await User.countDocuments({ isActive: true }),
                totalReports: await Report.countDocuments()
            };
        } else if (user.role === 'project_manager') {
            const managedProjects = await Project.find({ projectManager: user._id });
            const projectIds = managedProjects.map(p => p._id);
            
            stats = {
                totalProjects: managedProjects.length,
                activeProjects: managedProjects.filter(p => p.status === 'in_progress').length,
                totalTasks: await Task.countDocuments({ project: { $in: projectIds } }),
                completedTasks: await Task.countDocuments({ 
                    project: { $in: projectIds }, 
                    status: 'completed' 
                }),
                overdueProjects: managedProjects.filter(p => p.isOverdue()).length,
                totalReports: await Report.countDocuments({ project: { $in: projectIds } })
            };
        } else if (user.role === 'worker') {
            const assignedTasks = await Task.find({ assignedTo: user._id });
            const projectIds = [...new Set(assignedTasks.map(t => t.project))];
            
            stats = {
                totalTasks: assignedTasks.length,
                completedTasks: assignedTasks.filter(t => t.status === 'completed').length,
                inProgressTasks: assignedTasks.filter(t => t.status === 'in_progress').length,
                overdueTasks: assignedTasks.filter(t => t.isOverdue()).length,
                totalProjects: projectIds.length,
                totalReports: await Report.countDocuments({ reportedBy: user._id })
            };
        }
        
        res.json(stats);
    } catch (error) {
        console.error('Dashboard API stats error:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Calendar view for tasks and deadlines
router.get('/calendar', async (req, res) => {
    try {
        const user = req.session.user;
        let query = {};
        
        if (user.role === 'worker') {
            query.assignedTo = user._id;
        } else if (user.role === 'project_manager') {
            const projects = await Project.find({ projectManager: user._id }).select('_id');
            query.project = { $in: projects.map(p => p._id) };
        }
        
        const tasks = await Task.find(query)
            .populate('project', 'name')
            .populate('assignedTo', 'fullName')
            .select('title timeline status priority project assignedTo');
        
        // Format tasks for calendar
        const calendarEvents = tasks.map(task => ({
            id: task._id,
            title: task.title,
            start: task.timeline.startDate,
            end: task.timeline.endDate,
            color: getTaskColor(task.status, task.priority),
            extendedProps: {
                project: task.project.name,
                assignedTo: task.assignedTo.fullName,
                status: task.status,
                priority: task.priority
            }
        }));
        
        res.render('dashboard/calendar', {
            title: 'Lịch công việc',
            events: JSON.stringify(calendarEvents)
        });
    } catch (error) {
        console.error('Calendar error:', error);
        res.redirect('/dashboard');
    }
});

// Helper function to get task color based on status and priority
function getTaskColor(status, priority) {
    if (status === 'completed') return '#28a745';
    if (status === 'in_progress') return '#007bff';
    if (status === 'cancelled') return '#6c757d';
    
    // Color by priority for pending tasks
    switch (priority) {
        case 'urgent': return '#dc3545';
        case 'high': return '#fd7e14';
        case 'medium': return '#ffc107';
        case 'low': return '#17a2b8';
        default: return '#6c757d';
    }
}

module.exports = router;
