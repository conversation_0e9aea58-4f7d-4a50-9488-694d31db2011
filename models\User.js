const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        minlength: 3,
        maxlength: 50
    },
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    fullName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    phone: {
        type: String,
        trim: true,
        maxlength: 15
    },
    role: {
        type: String,
        enum: ['admin', 'project_manager', 'worker'],
        default: 'worker'
    },
    avatar: {
        type: String,
        default: null
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastLogin: {
        type: Date,
        default: null
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    
    try {
        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// Update updatedAt before saving
userSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

// Get user role permissions
userSchema.methods.getPermissions = function() {
    const permissions = {
        admin: ['create_project', 'edit_project', 'delete_project', 'manage_users', 'view_all_reports'],
        project_manager: ['create_project', 'edit_project', 'assign_tasks', 'view_project_reports'],
        worker: ['view_assigned_tasks', 'update_task_progress', 'create_reports']
    };
    return permissions[this.role] || [];
};

// Check if user has specific permission
userSchema.methods.hasPermission = function(permission) {
    return this.getPermissions().includes(permission);
};

module.exports = mongoose.model('User', userSchema);
