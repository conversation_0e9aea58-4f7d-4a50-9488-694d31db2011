const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
    project: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Project',
        required: true
    },
    task: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Task',
        default: null
    },
    reportedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    date: {
        type: Date,
        required: true,
        default: Date.now
    },
    type: {
        type: String,
        enum: ['daily', 'weekly', 'milestone', 'issue', 'completion'],
        default: 'daily'
    },
    workDescription: {
        type: String,
        required: true,
        maxlength: 1000
    },
    hoursWorked: {
        type: Number,
        required: true,
        min: 0,
        max: 24
    },
    progress: {
        previousProgress: {
            type: Number,
            default: 0,
            min: 0,
            max: 100
        },
        currentProgress: {
            type: Number,
            required: true,
            min: 0,
            max: 100
        }
    },
    issues: [{
        description: {
            type: String,
            required: true,
            maxlength: 500
        },
        severity: {
            type: String,
            enum: ['low', 'medium', 'high', 'critical'],
            default: 'medium'
        },
        status: {
            type: String,
            enum: ['open', 'in_progress', 'resolved'],
            default: 'open'
        },
        resolvedAt: {
            type: Date,
            default: null
        }
    }],
    materialsUsed: [{
        name: {
            type: String,
            required: true
        },
        quantity: {
            type: Number,
            required: true,
            min: 0
        },
        unit: {
            type: String,
            required: true
        },
        cost: {
            type: Number,
            default: 0,
            min: 0
        }
    }],
    weather: {
        condition: {
            type: String,
            enum: ['sunny', 'cloudy', 'rainy', 'stormy', 'foggy'],
            default: 'sunny'
        },
        temperature: {
            type: Number,
            default: null
        },
        impact: {
            type: String,
            enum: ['none', 'minor', 'moderate', 'severe'],
            default: 'none'
        }
    },
    teamPresent: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        hoursWorked: {
            type: Number,
            min: 0,
            max: 24
        },
        role: {
            type: String,
            maxlength: 50
        }
    }],
    images: [{
        name: {
            type: String,
            required: true
        },
        path: {
            type: String,
            required: true
        },
        description: {
            type: String,
            maxlength: 200
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    nextDayPlan: {
        type: String,
        maxlength: 500
    },
    supervisorNotes: {
        content: {
            type: String,
            maxlength: 500
        },
        addedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        addedAt: {
            type: Date,
            default: Date.now
        }
    },
    status: {
        type: String,
        enum: ['draft', 'submitted', 'reviewed', 'approved'],
        default: 'draft'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update updatedAt before saving
reportSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Calculate total material cost for this report
reportSchema.methods.getTotalMaterialCost = function() {
    return this.materialsUsed.reduce((total, material) => {
        return total + (material.quantity * material.cost);
    }, 0);
};

// Calculate total team hours
reportSchema.methods.getTotalTeamHours = function() {
    return this.teamPresent.reduce((total, member) => {
        return total + (member.hoursWorked || 0);
    }, 0);
};

// Check if report is overdue (should be submitted within 24 hours)
reportSchema.methods.isOverdue = function() {
    const deadline = new Date(this.date);
    deadline.setDate(deadline.getDate() + 1);
    return new Date() > deadline && this.status === 'draft';
};

module.exports = mongoose.model('Report', reportSchema);
