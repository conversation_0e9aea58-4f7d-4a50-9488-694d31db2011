<%- include('layouts/main', { body: `
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="text-center">
                <div class="error-icon mb-4">
                    <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
                </div>
                
                <h1 class="display-4 mb-3">${title}</h1>
                <p class="lead mb-4">${message}</p>
                
                <% if (error && error.stack && process.env.NODE_ENV === 'development') { %>
                <div class="alert alert-danger text-start">
                    <h6>Chi tiết lỗi (Development Mode):</h6>
                    <pre class="mb-0"><code>${error.stack}</code></pre>
                </div>
                <% } %>
                
                <div class="mt-4">
                    <a href="/dashboard" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>
                        Về trang chủ
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Quay lại
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

pre {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.8rem;
}
</style>
` }) %>
