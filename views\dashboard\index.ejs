<%- include('../layouts/main', { body: `
<!-- Statistics Cards -->
<div class="row mb-4">
    <% if (user.role === 'admin') { %>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tổng số dự án
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalProjects}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Dự án đang thực hiện
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.activeProjects}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hammer fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Tổng số nhiệm vụ
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Người dùng
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalUsers}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <% } else if (user.role === 'project_manager') { %>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Dự án quản lý
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalProjects}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Đang thực hiện
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.activeProjects}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hammer fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Nhiệm vụ
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Dự án trễ hạn
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.overdueProjects}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <% } else { %>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Nhiệm vụ của tôi
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.totalTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Hoàn thành
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.completedTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Đang thực hiện
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.inProgressTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Trễ hạn
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${stats.overdueTasks}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <% } %>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Projects Overview -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Dự án gần đây</h6>
                <a href="/projects" class="btn btn-sm btn-primary">Xem tất cả</a>
            </div>
            <div class="card-body">
                <% if (projects.length > 0) { %>
                <% projects.forEach(project => { %>
                <div class="row mb-3 p-3 border rounded priority-${project.priority}">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <a href="/projects/${project._id}" class="text-decoration-none">
                                ${project.name}
                            </a>
                        </h6>
                        <p class="text-muted mb-2">${project.description.substring(0, 100)}...</p>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            ${project.projectManager ? project.projectManager.fullName : 'Chưa phân công'}
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge status-${project.status} mb-2">${project.status}</span>
                        <div class="progress mb-2" style="height: 8px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: ${project.progress}%" 
                                 aria-valuenow="${project.progress}" 
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                        <small class="text-muted">${project.progress}% hoàn thành</small>
                    </div>
                </div>
                <% }); %>
                <% } else { %>
                <div class="text-center py-4">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Chưa có dự án nào</p>
                </div>
                <% } %>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Báo cáo gần đây</h6>
                <a href="/reports" class="btn btn-sm btn-primary">Xem tất cả</a>
            </div>
            <div class="card-body">
                <% if (recentReports.length > 0) { %>
                <% recentReports.forEach(report => { %>
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        <div class="icon-circle bg-primary">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="small text-gray-500">${moment(report.date).format('DD/MM/YYYY')}</div>
                        <div class="font-weight-bold">
                            <a href="/reports/${report._id}" class="text-decoration-none">
                                ${report.project.name}
                            </a>
                        </div>
                        <div class="small text-muted">
                            Bởi: ${report.reportedBy.fullName}
                        </div>
                    </div>
                </div>
                <% }); %>
                <% } else { %>
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                    <p class="text-muted small">Chưa có báo cáo nào</p>
                </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Nhiệm vụ gần đây</h6>
                <a href="/tasks" class="btn btn-sm btn-primary">Xem tất cả</a>
            </div>
            <div class="card-body">
                <% if (tasks.length > 0) { %>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nhiệm vụ</th>
                                <th>Dự án</th>
                                <th>Người thực hiện</th>
                                <th>Trạng thái</th>
                                <th>Tiến độ</th>
                                <th>Hạn chót</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% tasks.slice(0, 5).forEach(task => { %>
                            <tr>
                                <td>
                                    <a href="/tasks/${task._id}" class="text-decoration-none">
                                        ${task.title}
                                    </a>
                                </td>
                                <td>${task.project.name}</td>
                                <td>${task.assignedTo ? task.assignedTo.fullName : 'Chưa phân công'}</td>
                                <td>
                                    <span class="badge status-${task.status}">${task.status}</span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 6px; width: 80px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: ${task.progress}%" 
                                             aria-valuenow="${task.progress}" 
                                             aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                    <small>${task.progress}%</small>
                                </td>
                                <td>
                                    <small class="${moment(task.timeline.endDate).isBefore(moment()) && task.status !== 'completed' ? 'text-danger' : 'text-muted'}">
                                        ${moment(task.timeline.endDate).format('DD/MM/YYYY')}
                                    </small>
                                </td>
                            </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
                <% } else { %>
                <div class="text-center py-4">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Chưa có nhiệm vụ nào</p>
                </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-danger { border-left: 0.25rem solid #e74a3b !important; }

.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
` }) %>
