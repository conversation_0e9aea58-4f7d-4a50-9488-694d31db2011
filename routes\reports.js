const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const Report = require('../models/Report');
const Project = require('../models/Project');
const Task = require('../models/Task');
const User = require('../models/User');

// Configure multer for report images
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/reports/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'report-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Chỉ cho phép upload file ảnh'));
        }
    }
});

// List reports
router.get('/', async (req, res) => {
    try {
        const user = req.session.user;
        let query = {};
        
        // Filter reports based on user role
        if (user.role === 'worker') {
            query.reportedBy = user._id;
        } else if (user.role === 'project_manager') {
            // Get projects managed by this user
            const projects = await Project.find({ projectManager: user._id }).select('_id');
            query.project = { $in: projects.map(p => p._id) };
        }
        // Admin can see all reports (no filter)
        
        const reports = await Report.find(query)
            .populate('project', 'name')
            .populate('task', 'title')
            .populate('reportedBy', 'fullName')
            .sort({ date: -1 });
        
        res.render('reports/index', {
            title: 'Báo cáo tiến độ',
            reports: reports,
            user: user
        });
    } catch (error) {
        console.error('Reports list error:', error);
        res.status(500).render('error', {
            title: 'Lỗi',
            message: 'Không thể tải danh sách báo cáo',
            error: error
        });
    }
});

// Show create report form
router.get('/create', async (req, res) => {
    try {
        const user = req.session.user;
        let projectQuery = {};
        
        // Workers can only report on projects they're assigned to
        if (user.role === 'worker') {
            projectQuery['teamMembers.user'] = user._id;
        } else if (user.role === 'project_manager') {
            projectQuery.projectManager = user._id;
        }
        
        const projects = await Project.find(projectQuery).select('name');
        
        res.render('reports/create', {
            title: 'Tạo báo cáo mới',
            projects: projects,
            error: null,
            formData: {}
        });
    } catch (error) {
        console.error('Create report form error:', error);
        res.redirect('/reports');
    }
});

// Get tasks for a project (AJAX)
router.get('/project/:projectId/tasks', async (req, res) => {
    try {
        const user = req.session.user;
        let query = { project: req.params.projectId };
        
        // Workers can only see tasks assigned to them
        if (user.role === 'worker') {
            query.assignedTo = user._id;
        }
        
        const tasks = await Task.find(query).select('title status progress');
        res.json(tasks);
    } catch (error) {
        console.error('Get project tasks error:', error);
        res.status(500).json({ error: 'Server error' });
    }
});

// Create new report
router.post('/create', upload.array('images', 5), async (req, res) => {
    try {
        const {
            project, task, type, workDescription, hoursWorked,
            previousProgress, currentProgress, issues, materialsUsed,
            weatherCondition, weatherTemperature, weatherImpact,
            teamPresent, nextDayPlan
        } = req.body;
        
        // Validation
        if (!project || !workDescription || !hoursWorked || currentProgress === undefined) {
            const user = req.session.user;
            let projectQuery = {};
            
            if (user.role === 'worker') {
                projectQuery['teamMembers.user'] = user._id;
            } else if (user.role === 'project_manager') {
                projectQuery.projectManager = user._id;
            }
            
            const projects = await Project.find(projectQuery).select('name');
            
            return res.render('reports/create', {
                title: 'Tạo báo cáo mới',
                projects: projects,
                error: 'Vui lòng nhập đầy đủ thông tin bắt buộc',
                formData: req.body
            });
        }
        
        // Parse materials if provided
        let parsedMaterials = [];
        if (materialsUsed && Array.isArray(materialsUsed)) {
            parsedMaterials = materialsUsed.filter(m => m.name && m.quantity && m.unit).map(m => ({
                name: m.name,
                quantity: parseFloat(m.quantity),
                unit: m.unit,
                cost: parseFloat(m.cost) || 0
            }));
        }
        
        // Parse team members if provided
        let parsedTeam = [];
        if (teamPresent && Array.isArray(teamPresent)) {
            parsedTeam = teamPresent.filter(t => t.user && t.hoursWorked).map(t => ({
                user: t.user,
                hoursWorked: parseFloat(t.hoursWorked),
                role: t.role || 'worker'
            }));
        }
        
        // Parse issues if provided
        let parsedIssues = [];
        if (issues && Array.isArray(issues)) {
            parsedIssues = issues.filter(i => i.description).map(i => ({
                description: i.description,
                severity: i.severity || 'medium',
                status: 'open'
            }));
        }
        
        // Process uploaded images
        let images = [];
        if (req.files && req.files.length > 0) {
            images = req.files.map(file => ({
                name: file.originalname,
                path: file.path,
                description: req.body[`imageDesc_${file.fieldname}`] || ''
            }));
        }
        
        // Create report
        const report = new Report({
            project,
            task: task || null,
            reportedBy: req.session.user._id,
            type: type || 'daily',
            workDescription,
            hoursWorked: parseFloat(hoursWorked),
            progress: {
                previousProgress: parseFloat(previousProgress) || 0,
                currentProgress: parseFloat(currentProgress)
            },
            issues: parsedIssues,
            materialsUsed: parsedMaterials,
            weather: {
                condition: weatherCondition || 'sunny',
                temperature: weatherTemperature ? parseFloat(weatherTemperature) : null,
                impact: weatherImpact || 'none'
            },
            teamPresent: parsedTeam,
            images: images,
            nextDayPlan: nextDayPlan || null,
            status: 'submitted'
        });
        
        await report.save();
        
        // Update task progress if task is specified
        if (task) {
            const taskDoc = await Task.findById(task);
            if (taskDoc) {
                taskDoc.progress = parseFloat(currentProgress);
                taskDoc.actualHours += parseFloat(hoursWorked);
                await taskDoc.save();
            }
        }
        
        res.redirect('/reports');
        
    } catch (error) {
        console.error('Create report error:', error);
        const user = req.session.user;
        let projectQuery = {};
        
        if (user.role === 'worker') {
            projectQuery['teamMembers.user'] = user._id;
        } else if (user.role === 'project_manager') {
            projectQuery.projectManager = user._id;
        }
        
        const projects = await Project.find(projectQuery).select('name');
        
        res.render('reports/create', {
            title: 'Tạo báo cáo mới',
            projects: projects,
            error: 'Đã xảy ra lỗi, vui lòng thử lại',
            formData: req.body
        });
    }
});

// View report details
router.get('/:id', async (req, res) => {
    try {
        const report = await Report.findById(req.params.id)
            .populate('project', 'name')
            .populate('task', 'title')
            .populate('reportedBy', 'fullName email')
            .populate('teamPresent.user', 'fullName')
            .populate('supervisorNotes.addedBy', 'fullName');
        
        if (!report) {
            return res.redirect('/reports');
        }
        
        // Check access permissions
        const user = req.session.user;
        if (user.role === 'worker' && report.reportedBy._id.toString() !== user._id) {
            return res.status(403).render('error', {
                title: 'Truy cập bị từ chối',
                message: 'Bạn không có quyền xem báo cáo này',
                error: {}
            });
        }
        
        res.render('reports/detail', {
            title: `Báo cáo - ${report.project.name}`,
            report: report
        });
    } catch (error) {
        console.error('Report detail error:', error);
        res.redirect('/reports');
    }
});

module.exports = router;
