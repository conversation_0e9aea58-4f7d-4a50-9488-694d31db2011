const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { requireGuest, requireAuth } = require('../middleware/auth');

// Login page
router.get('/login', requireGuest, (req, res) => {
    res.render('auth/login', {
        title: 'Đăng nhập',
        error: null
    });
});

// Login process
router.post('/login', requireGuest, async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.render('auth/login', {
                title: 'Đăng nhập',
                error: '<PERSON>ui lòng nhập đầy đủ thông tin'
            });
        }
        
        // Find user by username or email
        const user = await User.findOne({
            $or: [
                { username: username },
                { email: username }
            ]
        });
        
        if (!user || !user.isActive) {
            return res.render('auth/login', {
                title: '<PERSON><PERSON><PERSON> nhập',
                error: '<PERSON><PERSON><PERSON> khoản không tồn tại hoặc đã bị khóa'
            });
        }
        
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            return res.render('auth/login', {
                title: 'Đăng nhập',
                error: 'Mật khẩu không chính xác'
            });
        }
        
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        
        // Create session
        req.session.user = {
            _id: user._id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role,
            avatar: user.avatar
        };
        
        res.redirect('/dashboard');
        
    } catch (error) {
        console.error('Login error:', error);
        res.render('auth/login', {
            title: 'Đăng nhập',
            error: 'Đã xảy ra lỗi, vui lòng thử lại'
        });
    }
});

// Register page
router.get('/register', requireGuest, (req, res) => {
    res.render('auth/register', {
        title: 'Đăng ký',
        error: null,
        formData: {}
    });
});

// Register process
router.post('/register', requireGuest, async (req, res) => {
    try {
        const { username, email, password, confirmPassword, fullName, phone } = req.body;
        
        // Validation
        if (!username || !email || !password || !fullName) {
            return res.render('auth/register', {
                title: 'Đăng ký',
                error: 'Vui lòng nhập đầy đủ thông tin bắt buộc',
                formData: req.body
            });
        }
        
        if (password !== confirmPassword) {
            return res.render('auth/register', {
                title: 'Đăng ký',
                error: 'Mật khẩu xác nhận không khớp',
                formData: req.body
            });
        }
        
        if (password.length < 6) {
            return res.render('auth/register', {
                title: 'Đăng ký',
                error: 'Mật khẩu phải có ít nhất 6 ký tự',
                formData: req.body
            });
        }
        
        // Check if user exists
        const existingUser = await User.findOne({
            $or: [
                { username: username },
                { email: email }
            ]
        });
        
        if (existingUser) {
            return res.render('auth/register', {
                title: 'Đăng ký',
                error: 'Tên đăng nhập hoặc email đã tồn tại',
                formData: req.body
            });
        }
        
        // Create new user
        const user = new User({
            username,
            email,
            password,
            fullName,
            phone: phone || null,
            role: 'worker' // Default role
        });
        
        await user.save();
        
        // Auto login after registration
        req.session.user = {
            _id: user._id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role,
            avatar: user.avatar
        };
        
        res.redirect('/dashboard');
        
    } catch (error) {
        console.error('Register error:', error);
        res.render('auth/register', {
            title: 'Đăng ký',
            error: 'Đã xảy ra lỗi, vui lòng thử lại',
            formData: req.body
        });
    }
});

// Logout
router.post('/logout', requireAuth, (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('Logout error:', err);
        }
        res.redirect('/auth/login');
    });
});

// Profile page
router.get('/profile', requireAuth, async (req, res) => {
    try {
        const user = await User.findById(req.session.user._id).select('-password');
        res.render('auth/profile', {
            title: 'Thông tin cá nhân',
            user: user,
            success: null,
            error: null
        });
    } catch (error) {
        console.error('Profile error:', error);
        res.redirect('/dashboard');
    }
});

// Update profile
router.post('/profile', requireAuth, async (req, res) => {
    try {
        const { fullName, phone, currentPassword, newPassword, confirmPassword } = req.body;
        const user = await User.findById(req.session.user._id);
        
        if (!user) {
            return res.redirect('/auth/login');
        }
        
        // Update basic info
        user.fullName = fullName;
        user.phone = phone || null;
        
        // Update password if provided
        if (newPassword) {
            if (!currentPassword) {
                return res.render('auth/profile', {
                    title: 'Thông tin cá nhân',
                    user: user,
                    success: null,
                    error: 'Vui lòng nhập mật khẩu hiện tại'
                });
            }
            
            const isMatch = await user.comparePassword(currentPassword);
            if (!isMatch) {
                return res.render('auth/profile', {
                    title: 'Thông tin cá nhân',
                    user: user,
                    success: null,
                    error: 'Mật khẩu hiện tại không chính xác'
                });
            }
            
            if (newPassword !== confirmPassword) {
                return res.render('auth/profile', {
                    title: 'Thông tin cá nhân',
                    user: user,
                    success: null,
                    error: 'Mật khẩu mới không khớp'
                });
            }
            
            if (newPassword.length < 6) {
                return res.render('auth/profile', {
                    title: 'Thông tin cá nhân',
                    user: user,
                    success: null,
                    error: 'Mật khẩu mới phải có ít nhất 6 ký tự'
                });
            }
            
            user.password = newPassword;
        }
        
        await user.save();
        
        // Update session
        req.session.user.fullName = user.fullName;
        
        res.render('auth/profile', {
            title: 'Thông tin cá nhân',
            user: user,
            success: 'Cập nhật thông tin thành công',
            error: null
        });
        
    } catch (error) {
        console.error('Update profile error:', error);
        const user = await User.findById(req.session.user._id).select('-password');
        res.render('auth/profile', {
            title: 'Thông tin cá nhân',
            user: user,
            success: null,
            error: 'Đã xảy ra lỗi, vui lòng thử lại'
        });
    }
});

module.exports = router;
